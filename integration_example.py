#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
彩票预测系统与优化系统集成示例
展示两个系统如何协同工作
"""

import sys
import os

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    # 导入原始彩票预测系统
    from lottery_analyzer import LotteryAnalyzer, LotteryDataManager, ZodiacAnalyzer, PredictionEngine
    
    # 导入优化系统
    from optimization_integration import OptimizationIntegration
    
    print("✅ 成功导入两个系统")
    
    class IntegratedLotterySystem:
        """集成彩票预测系统"""
        
        def __init__(self):
            # 初始化原始系统
            self.data_manager = LotteryDataManager()
            self.analyzer = ZodiacAnalyzer(self.data_manager)
            self.prediction_engine = PredictionEngine(self.data_manager, self.analyzer)
            self.lottery_analyzer = LotteryAnalyzer()  # 不需要参数
            
            # 初始化优化系统
            self.optimizer = OptimizationIntegration(self.data_manager)
            
            print("✅ 集成系统初始化完成")
        
        def run_comparison_analysis(self, history_data):
            """运行对比分析"""
            print("\n" + "="*60)
            print("🔍 原始系统 vs 优化系统对比分析")
            print("="*60)
            
            # 1. 原始系统预测
            print("\n📊 原始系统预测结果:")
            original_recommendations = self.prediction_engine.get_recommendations(history_data, top_n=3)
            for i, rec in enumerate(original_recommendations, 1):
                print(f"  推荐 {i}: {rec.zodiac} (得分: {rec.score:.3f}, 置信度: {rec.confidence})")
            
            # 2. 优化系统预测
            print("\n🚀 优化系统预测结果:")
            optimized_recommendations = self.optimizer.get_final_recommendations(history_data, top_n=3)
            for i, rec in enumerate(optimized_recommendations, 1):
                print(f"  推荐 {i}: {rec['zodiac']} (概率: {rec['probability']:.3f}, 置信度: {rec['confidence']})")
            
            # 3. 对比分析
            print("\n📈 系统对比分析:")
            print("  原始系统特点:")
            print("    - 基于历史频率和趋势分析")
            print("    - 计算速度快，结果稳定")
            print("    - 适合基础预测需求")
            
            print("\n  优化系统特点:")
            print("    - 多阶段高级算法集成")
            print("    - 深度学习 + 强化学习")
            print("    - 不确定性量化")
            print("    - 自适应参数调整")
            
            return {
                'original': original_recommendations,
                'optimized': optimized_recommendations
            }
        
        def get_integrated_recommendations(self, history_data, top_n=3):
            """获取集成推荐（结合两个系统的优势）"""
            print("\n🎯 生成集成推荐...")
            
            # 获取两个系统的推荐
            original_recs = self.prediction_engine.get_recommendations(history_data, top_n=top_n)
            optimized_recs = self.optimizer.get_final_recommendations(history_data, top_n=top_n)
            
            # 集成策略：结合两个系统的推荐
            integrated_recs = []
            
            # 从原始系统获取基础推荐
            for rec in original_recs:
                integrated_recs.append({
                    'zodiac': rec.zodiac,
                    'score': rec.score,
                    'confidence': rec.confidence,
                    'source': 'Original System',
                    'reasons': rec.reasons
                })
            
            # 从优化系统获取高级推荐
            for rec in optimized_recs:
                # 检查是否已存在
                existing = next((r for r in integrated_recs if r['zodiac'] == rec['zodiac']), None)
                if existing:
                    # 如果存在，更新为优化系统的结果
                    existing.update({
                        'score': rec['probability'],
                        'confidence': rec['confidence'],
                        'source': 'Integrated (Original + Optimized)',
                        'optimization_phases': rec['optimization_phases']
                    })
                else:
                    integrated_recs.append({
                        'zodiac': rec['zodiac'],
                        'score': rec['probability'],
                        'confidence': rec['confidence'],
                        'source': 'Optimized System',
                        'optimization_phases': rec['optimization_phases']
                    })
            
            # 按得分排序
            integrated_recs.sort(key=lambda x: x['score'], reverse=True)
            
            return integrated_recs[:top_n]
    
    # 使用示例
    if __name__ == "__main__":
        print("🎲 彩票预测系统集成演示")
        print("="*50)
        
        # 创建集成系统
        integrated_system = IntegratedLotterySystem()
        
        # 模拟历史数据
        mock_history_data = [
            {'openCode': '1,2,3,4,5,6,7', 'expect': '2025001'},
            {'openCode': '8,9,10,11,12,13,14', 'expect': '2025002'},
            {'openCode': '15,16,17,18,19,20,21', 'expect': '2025003'},
            {'openCode': '22,23,24,25,26,27,28', 'expect': '2025004'},
            {'openCode': '29,30,31,32,33,34,35', 'expect': '2025005'},
        ]
        
        # 运行对比分析
        comparison_results = integrated_system.run_comparison_analysis(mock_history_data)
        
        # 获取集成推荐
        integrated_recommendations = integrated_system.get_integrated_recommendations(mock_history_data, top_n=3)
        
        print("\n🎯 最终集成推荐结果:")
        print("="*50)
        for i, rec in enumerate(integrated_recommendations, 1):
            print(f"推荐 {i}: {rec['zodiac']}")
            print(f"  得分/概率: {rec['score']:.3f}")
            print(f"  置信度: {rec['confidence']}")
            print(f"  来源: {rec['source']}")
            if 'optimization_phases' in rec:
                print(f"  优化阶段: {', '.join(rec['optimization_phases'])}")
            print()
        
        print("✅ 集成系统演示完成！")

except ImportError as e:
    print(f"❌ 导入错误: {e}")
    print("请确保 lottery_analyzer.py 和 optimization_integration.py 文件存在")
except Exception as e:
    print(f"❌ 运行错误: {e}")
    import traceback
    traceback.print_exc()
