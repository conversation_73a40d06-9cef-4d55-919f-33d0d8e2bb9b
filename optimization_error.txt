错误: 'MockDataManager' object has no attribute 'max_number'
Traceback (most recent call last):
  File "D:\web_pom_playwright\direct_test.py", line 27, in <module>
    recommendations = optimizer.get_final_recommendations(mock_history_data, top_n=3)
  File "D:\web_pom_playwright\optimization_integration.py", line 274, in get_final_recommendations
    optimization_results = self.run_complete_optimization(history_data)
  File "D:\web_pom_playwright\optimization_integration.py", line 34, in run_complete_optimization
    phase_two_results = self._run_phase_two_optimization(history_data)
  File "D:\web_pom_playwright\optimization_integration.py", line 69, in _run_phase_two_optimization
    dl_predictions = self._deep_learning_prediction(history_data)
  File "D:\web_pom_playwright\optimization_integration.py", line 157, in _deep_learning_prediction
    zodiacs = {self.data_manager.get_zodiac_info(i).zodiac for i in range(1, self.data_manager.max_number + 1)}
                                                                             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'MockDataManager' object has no attribute 'max_number'
