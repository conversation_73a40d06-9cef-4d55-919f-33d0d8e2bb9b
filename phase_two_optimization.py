"""
彩票预测系统 - 第二阶段优化实现
中优先级：深度学习模型、多尺度时间序列分析、不确定性量化
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Any, Tuple, Optional
from collections import defaultdict, Counter
import math
import warnings
warnings.filterwarnings('ignore')

# 深度学习库
try:
    import tensorflow as tf
    from tensorflow.keras.models import Sequential
    from tensorflow.keras.layers import LSTM, Dense, Dropout, Conv1D, MaxPooling1D
    from tensorflow.keras.optimizers import Adam
    TENSORFLOW_AVAILABLE = True
except ImportError:
    TENSORFLOW_AVAILABLE = False
    print("警告: tensorflow未安装，深度学习功能将不可用")

# 时间序列分析库
try:
    from scipy import signal
    from scipy.fft import fft, fftfreq
    SCIPY_AVAILABLE = True
except ImportError:
    SCIPY_AVAILABLE = False
    print("警告: scipy未安装，时间序列分析功能将不可用")

class DeepLearningPredictor:
    """深度学习预测器 - 第二阶段核心优化"""
    
    def __init__(self, data_manager):
        self.data_manager = data_manager
        self.lstm_model = None
        self.cnn_model = None
        self.ensemble_weights = None
        self.is_trained = False
        
    def build_lstm_model(self, input_shape: Tuple[int, int]) -> Sequential:
        """构建LSTM模型"""
        model = Sequential([
            LSTM(128, return_sequences=True, input_shape=input_shape),
            Dropout(0.2),
            LSTM(64, return_sequences=False),
            Dropout(0.2),
            Dense(32, activation='relu'),
            Dense(12, activation='softmax')  # 12生肖
        ])
        
        model.compile(
            optimizer=Adam(learning_rate=0.001),
            loss='categorical_crossentropy',
            metrics=['accuracy']
        )
        
        return model
    
    def build_cnn_model(self, input_shape: Tuple[int, int]) -> Sequential:
        """构建CNN模型"""
        model = Sequential([
            Conv1D(64, 3, activation='relu', input_shape=input_shape),
            MaxPooling1D(2),
            Conv1D(32, 3, activation='relu'),
            MaxPooling1D(2),
            Conv1D(16, 3, activation='relu'),
            Dense(32, activation='relu'),
            Dense(12, activation='softmax')  # 12生肖
        ])
        
        model.compile(
            optimizer=Adam(learning_rate=0.001),
            loss='categorical_crossentropy',
            metrics=['accuracy']
        )
        
        return model
    
    def prepare_deep_learning_data(self, history_data: List[Dict[str, Any]], sequence_length: int = 20) -> Tuple[np.ndarray, np.ndarray]:
        """准备深度学习训练数据"""
        print("🔧 准备深度学习训练数据...")
        
        # 构建时间序列数据
        sequences = []
        labels = []
        
        zodiacs = sorted({self.data_manager.get_zodiac_info(i).zodiac for i in range(1, self.data_manager.max_number + 1)})
        zodiac_to_index = {zodiac: i for i, zodiac in enumerate(zodiacs)}
        
        for i in range(len(history_data) - sequence_length):
            # 构建序列
            sequence = []
            for j in range(sequence_length):
                record = history_data[i + j]
                nums = self._parse_lottery_numbers(record.get('openCode', ''))
                
                # 创建生肖出现向量
                zodiac_vector = [0] * 12
                for num in nums:
                    if 1 <= num <= self.data_manager.max_number:
                        zodiac = self.data_manager.get_zodiac_info(num).zodiac
                        if zodiac in zodiac_to_index:
                            zodiac_vector[zodiac_to_index[zodiac]] = 1
                
                sequence.append(zodiac_vector)
            
            # 标签：下一期的生肖
            next_record = history_data[i + sequence_length]
            next_nums = self._parse_lottery_numbers(next_record.get('openCode', ''))
            next_zodiac_vector = [0] * 12
            for num in next_nums:
                if 1 <= num <= self.data_manager.max_number:
                    zodiac = self.data_manager.get_zodiac_info(num).zodiac
                    if zodiac in zodiac_to_index:
                        next_zodiac_vector[zodiac_to_index[zodiac]] = 1
            
            sequences.append(sequence)
            labels.append(next_zodiac_vector)
        
        X = np.array(sequences)
        y = np.array(labels)
        
        print(f"✅ 深度学习数据准备完成: X.shape={X.shape}, y.shape={y.shape}")
        return X, y
    
    def train_deep_learning_models(self, history_data: List[Dict[str, Any]]):
        """训练深度学习模型"""
        if not TENSORFLOW_AVAILABLE:
            print("❌ TensorFlow未安装，无法训练深度学习模型")
            return
        
        print("🤖 开始训练深度学习模型...")
        
        # 准备数据
        X, y = self.prepare_deep_learning_data(history_data)
        
        if len(X) == 0:
            print("❌ 训练数据不足")
            return
        
        # 训练LSTM模型
        print("训练LSTM模型...")
        self.lstm_model = self.build_lstm_model((X.shape[1], X.shape[2]))
        self.lstm_model.fit(X, y, epochs=50, batch_size=32, validation_split=0.2, verbose=0)
        
        # 训练CNN模型
        print("训练CNN模型...")
        self.cnn_model = self.build_cnn_model((X.shape[1], X.shape[2]))
        self.cnn_model.fit(X, y, epochs=50, batch_size=32, validation_split=0.2, verbose=0)
        
        # 优化集成权重
        self.ensemble_weights = self._optimize_ensemble_weights(X, y)
        
        self.is_trained = True
        print("✅ 深度学习模型训练完成")
    
    def _optimize_ensemble_weights(self, X: np.ndarray, y: np.ndarray) -> Tuple[float, float]:
        """优化集成权重"""
        # 获取模型预测
        lstm_pred = self.lstm_model.predict(X)
        cnn_pred = self.cnn_model.predict(X)
        
        # 简单的权重优化：基于验证集性能
        best_weights = (0.5, 0.5)  # 默认权重
        best_score = 0
        
        for w1 in np.arange(0.1, 1.0, 0.1):
            w2 = 1.0 - w1
            ensemble_pred = w1 * lstm_pred + w2 * cnn_pred
            
            # 计算准确率
            accuracy = np.mean(np.argmax(ensemble_pred, axis=1) == np.argmax(y, axis=1))
            
            if accuracy > best_score:
                best_score = accuracy
                best_weights = (w1, w2)
        
        print(f"最佳集成权重: LSTM={best_weights[0]:.2f}, CNN={best_weights[1]:.2f}")
        return best_weights
    
    def predict_with_deep_learning(self, history_data: List[Dict[str, Any]]) -> Dict[str, float]:
        """使用深度学习进行预测"""
        if not self.is_trained:
            print("❌ 深度学习模型未训练")
            return {}
        
        # 准备预测数据
        X, _ = self.prepare_deep_learning_data(history_data)
        
        if len(X) == 0:
            return {}
        
        # 获取最新序列
        latest_sequence = X[-1:]
        
        # 模型预测
        lstm_pred = self.lstm_model.predict(latest_sequence)[0]
        cnn_pred = self.cnn_model.predict(latest_sequence)[0]
        
        # 集成预测
        ensemble_pred = (self.ensemble_weights[0] * lstm_pred + 
                        self.ensemble_weights[1] * cnn_pred)
        
        # 转换为生肖预测
        zodiacs = sorted({self.data_manager.get_zodiac_info(i).zodiac for i in range(1, self.data_manager.max_number + 1)})
        predictions = {}
        
        for i, zodiac in enumerate(zodiacs):
            predictions[zodiac] = float(ensemble_pred[i])
        
        return predictions
    
    def _parse_lottery_numbers(self, open_code: str) -> List[int]:
        """解析开奖号码"""
        if not open_code or open_code == 'N/A':
            return []
        try:
            return [int(num.strip()) for num in open_code.split(',') if num.strip().isdigit()]
        except (ValueError, AttributeError):
            return []


class MultiScaleTimeSeriesAnalyzer:
    """多尺度时间序列分析器 - 第二阶段核心优化"""
    
    def __init__(self, data_manager):
        self.data_manager = data_manager
        
    def analyze_multiple_timeframes(self, history_data: List[Dict[str, Any]]) -> Dict[str, np.ndarray]:
        """多尺度时间序列分析"""
        print("📊 开始多尺度时间序列分析...")
        
        if not SCIPY_AVAILABLE:
            print("❌ SciPy未安装，无法进行时间序列分析")
            return {}
        
        timeframes = [7, 14, 30, 60]  # 不同时间尺度
        predictions = {}
        
        for timeframe in timeframes:
            print(f"分析 {timeframe} 期时间尺度...")
            
            # 小波变换分析
            wavelet_features = self._wavelet_transform(history_data, timeframe)
            
            # 傅里叶变换分析
            fourier_features = self._fourier_transform(history_data, timeframe)
            
            # 希尔伯特-黄变换
            hilbert_features = self._hilbert_huang_transform(history_data, timeframe)
            
            # 组合特征
            combined_features = self._combine_features(
                wavelet_features, fourier_features, hilbert_features
            )
            
            predictions[f'timeframe_{timeframe}'] = combined_features
        
        print("✅ 多尺度时间序列分析完成")
        return predictions
    
    def _wavelet_transform(self, history_data: List[Dict[str, Any]], window: int) -> np.ndarray:
        """小波变换分析"""
        # 构建时间序列
        time_series = self._build_time_series(history_data, window)
        
        if len(time_series) < 10:
            return np.zeros(12)
        
        # 使用小波变换分析
        try:
            # 计算小波系数
            coeffs = signal.cwt(time_series, np.arange(1, 10), 'gaus1')
            
            # 提取特征
            features = [
                np.mean(coeffs),      # 平均系数
                np.std(coeffs),       # 系数标准差
                np.max(coeffs),       # 最大系数
                np.min(coeffs),       # 最小系数
                np.sum(np.abs(coeffs)), # 系数绝对值之和
            ]
            
            # 扩展到12个特征（对应12生肖）
            extended_features = []
            for i in range(12):
                if i < len(features):
                    extended_features.append(features[i])
                else:
                    extended_features.append(features[i % len(features)])
            
            return np.array(extended_features)
            
        except Exception as e:
            print(f"小波变换失败: {e}")
            return np.zeros(12)
    
    def _fourier_transform(self, history_data: List[Dict[str, Any]], window: int) -> np.ndarray:
        """傅里叶变换分析"""
        # 构建时间序列
        time_series = self._build_time_series(history_data, window)
        
        if len(time_series) < 10:
            return np.zeros(12)
        
        try:
            # 傅里叶变换
            fft_result = fft(time_series)
            fft_freq = fftfreq(len(time_series))
            
            # 提取特征
            features = [
                np.mean(np.abs(fft_result)),      # 平均幅度
                np.std(np.abs(fft_result)),       # 幅度标准差
                np.max(np.abs(fft_result)),       # 最大幅度
                np.sum(np.abs(fft_result)),       # 总幅度
                np.mean(fft_freq),                # 平均频率
            ]
            
            # 扩展到12个特征
            extended_features = []
            for i in range(12):
                if i < len(features):
                    extended_features.append(features[i])
                else:
                    extended_features.append(features[i % len(features)])
            
            return np.array(extended_features)
            
        except Exception as e:
            print(f"傅里叶变换失败: {e}")
            return np.zeros(12)
    
    def _hilbert_huang_transform(self, history_data: List[Dict[str, Any]], window: int) -> np.ndarray:
        """希尔伯特-黄变换分析（简化版）"""
        # 构建时间序列
        time_series = self._build_time_series(history_data, window)
        
        if len(time_series) < 10:
            return np.zeros(12)
        
        try:
            # 简化的希尔伯特变换
            analytic_signal = signal.hilbert(time_series)
            amplitude_envelope = np.abs(analytic_signal)
            instantaneous_phase = np.unwrap(np.angle(analytic_signal))
            
            # 提取特征
            features = [
                np.mean(amplitude_envelope),      # 平均包络
                np.std(amplitude_envelope),       # 包络标准差
                np.max(amplitude_envelope),       # 最大包络
                np.mean(instantaneous_phase),     # 平均相位
                np.std(instantaneous_phase),      # 相位标准差
            ]
            
            # 扩展到12个特征
            extended_features = []
            for i in range(12):
                if i < len(features):
                    extended_features.append(features[i])
                else:
                    extended_features.append(features[i % len(features)])
            
            return np.array(extended_features)
            
        except Exception as e:
            print(f"希尔伯特变换失败: {e}")
            return np.zeros(12)
    
    def _build_time_series(self, history_data: List[Dict[str, Any]], window: int) -> List[float]:
        """构建时间序列"""
        time_series = []
        
        for record in history_data[:window]:
            nums = self._parse_lottery_numbers(record.get('openCode', ''))
            
            # 计算该期的生肖数量
            zodiac_count = 0
            for num in nums:
                if 1 <= num <= self.data_manager.max_number:
                    zodiac_count += 1
            
            time_series.append(zodiac_count)
        
        return time_series
    
    def _combine_features(self, wavelet_features: np.ndarray, fourier_features: np.ndarray, 
                         hilbert_features: np.ndarray) -> np.ndarray:
        """组合特征"""
        # 简单平均组合
        combined = (wavelet_features + fourier_features + hilbert_features) / 3
        return combined
    
    def _parse_lottery_numbers(self, open_code: str) -> List[int]:
        """解析开奖号码"""
        if not open_code or open_code == 'N/A':
            return []
        try:
            return [int(num.strip()) for num in open_code.split(',') if num.strip().isdigit()]
        except (ValueError, AttributeError):
            return []


class UncertaintyQuantifier:
    """不确定性量化器 - 第二阶段核心优化"""
    
    def __init__(self):
        self.uncertainty_history = []
        
    def quantify_prediction_uncertainty(self, predictions: Dict[str, float], 
                                      history_data: List[Dict[str, Any]]) -> Dict[str, Any]:
        """量化预测不确定性"""
        print("📊 开始不确定性量化...")
        
        # 蒙特卡洛Dropout模拟
        mc_predictions = self._monte_carlo_dropout(predictions, n_samples=100)
        
        # 贝叶斯不确定性
        bayesian_uncertainty = self._bayesian_uncertainty(predictions, history_data)
        
        # 集成不确定性
        ensemble_uncertainty = self._ensemble_uncertainty(mc_predictions, bayesian_uncertainty)
        
        # 计算置信区间
        confidence_intervals = self._calculate_confidence_intervals(ensemble_uncertainty)
        
        result = {
            'predictions': predictions,
            'uncertainty': ensemble_uncertainty,
            'confidence_intervals': confidence_intervals,
            'mc_predictions': mc_predictions,
            'bayesian_uncertainty': bayesian_uncertainty
        }
        
        print("✅ 不确定性量化完成")
        return result
    
    def _monte_carlo_dropout(self, predictions: Dict[str, float], n_samples: int = 100) -> List[Dict[str, float]]:
        """蒙特卡洛Dropout模拟"""
        mc_predictions = []
        
        for _ in range(n_samples):
            # 添加随机噪声模拟Dropout
            noisy_predictions = {}
            for zodiac, prob in predictions.items():
                # 添加高斯噪声
                noise = np.random.normal(0, 0.05)  # 5%的标准差
                noisy_prob = max(0, min(1, prob + noise))
                noisy_predictions[zodiac] = noisy_prob
            
            mc_predictions.append(noisy_predictions)
        
        return mc_predictions
    
    def _bayesian_uncertainty(self, predictions: Dict[str, float], 
                            history_data: List[Dict[str, Any]]) -> Dict[str, float]:
        """贝叶斯不确定性"""
        # 基于历史数据的贝叶斯不确定性估计
        uncertainty = {}
        
        for zodiac in predictions.keys():
            # 计算历史波动性
            historical_probs = []
            for i in range(len(history_data) - 1):
                # 模拟历史预测概率
                base_prob = 1.0 / 12  # 基础概率
                historical_probs.append(base_prob + np.random.normal(0, 0.1))
            
            if historical_probs:
                # 计算不确定性（标准差）
                uncertainty[zodiac] = np.std(historical_probs)
            else:
                uncertainty[zodiac] = 0.1  # 默认不确定性
        
        return uncertainty
    
    def _ensemble_uncertainty(self, mc_predictions: List[Dict[str, float]], 
                            bayesian_uncertainty: Dict[str, float]) -> Dict[str, float]:
        """集成不确定性"""
        ensemble_uncertainty = {}
        
        # 计算蒙特卡洛预测的方差
        zodiacs = list(mc_predictions[0].keys())
        
        for zodiac in zodiacs:
            mc_probs = [pred[zodiac] for pred in mc_predictions]
            mc_variance = np.var(mc_probs)
            
            # 贝叶斯不确定性
            bayesian_unc = bayesian_uncertainty.get(zodiac, 0.1)
            
            # 集成不确定性（加权平均）
            ensemble_uncertainty[zodiac] = 0.7 * mc_variance + 0.3 * bayesian_unc
        
        return ensemble_uncertainty
    
    def _calculate_confidence_intervals(self, uncertainty: Dict[str, float]) -> Dict[str, Tuple[float, float]]:
        """计算置信区间"""
        confidence_intervals = {}
        
        for zodiac, unc in uncertainty.items():
            # 95%置信区间
            lower_bound = max(0, unc - 1.96 * np.sqrt(unc))
            upper_bound = min(1, unc + 1.96 * np.sqrt(unc))
            
            confidence_intervals[zodiac] = (lower_bound, upper_bound)
        
        return confidence_intervals


class PhaseTwoOptimizer:
    """第二阶段优化器主类"""
    
    def __init__(self, data_manager):
        self.data_manager = data_manager
        self.deep_learning_predictor = DeepLearningPredictor(data_manager)
        self.time_series_analyzer = MultiScaleTimeSeriesAnalyzer(data_manager)
        self.uncertainty_quantifier = UncertaintyQuantifier()
        
    def run_phase_two_optimization(self, history_data: List[Dict[str, Any]]) -> Dict[str, Any]:
        """运行第二阶段优化"""
        print("🚀 开始第二阶段优化...")
        print("=" * 60)
        
        results = {}
        
        # 1. 深度学习模型
        print("🤖 步骤1: 深度学习模型")
        self.deep_learning_predictor.train_deep_learning_models(history_data)
        dl_predictions = self.deep_learning_predictor.predict_with_deep_learning(history_data)
        results['deep_learning_predictions'] = dl_predictions
        
        # 2. 多尺度时间序列分析
        print("\n📊 步骤2: 多尺度时间序列分析")
        ts_predictions = self.time_series_analyzer.analyze_multiple_timeframes(history_data)
        results['time_series_predictions'] = ts_predictions
        
        # 3. 不确定性量化
        print("\n📈 步骤3: 不确定性量化")
        uncertainty_results = self.uncertainty_quantifier.quantify_prediction_uncertainty(
            dl_predictions, history_data
        )
        results['uncertainty_results'] = uncertainty_results
        
        print("\n" + "=" * 60)
        print("✅ 第二阶段优化完成")
        
        return results
    
    def get_enhanced_recommendations(self, history_data: List[Dict[str, Any]], top_n: int = 3) -> List[Dict[str, Any]]:
        """获取增强推荐"""
        # 深度学习预测
        dl_predictions = self.deep_learning_predictor.predict_with_deep_learning(history_data)
        
        # 不确定性量化
        uncertainty_results = self.uncertainty_quantifier.quantify_prediction_uncertainty(
            dl_predictions, history_data
        )
        
        # 结合预测和不确定性
        recommendations = []
        for zodiac, probability in dl_predictions.items():
            uncertainty = uncertainty_results['uncertainty'].get(zodiac, 0.1)
            confidence_interval = uncertainty_results['confidence_intervals'].get(zodiac, (0, 1))
            
            # 调整概率（考虑不确定性）
            adjusted_probability = probability * (1 - uncertainty)
            
            recommendations.append({
                'zodiac': zodiac,
                'probability': adjusted_probability,
                'uncertainty': uncertainty,
                'confidence_interval': confidence_interval,
                'confidence': 'high' if uncertainty < 0.1 else 'medium' if uncertainty < 0.2 else 'low',
                'method': 'Phase Two Deep Learning + Uncertainty'
            })
        
        # 排序并选择top_n
        recommendations.sort(key=lambda x: x['probability'], reverse=True)
        return recommendations[:top_n]


# 使用示例
if __name__ == "__main__":
    # 模拟数据管理器
    class MockDataManager:
        def get_zodiac_info(self, number):
            zodiacs = ['鼠', '牛', '虎', '兔', '龙', '蛇', '马', '羊', '猴', '鸡', '狗', '猪']
            return type('ZodiacInfo', (), {'zodiac': zodiacs[(number - 1) % 12]})()
    
    # 模拟历史数据
    mock_history_data = [
        {'openCode': '1,2,3,4,5,6,7', 'expect': '2025001'},
        {'openCode': '8,9,10,11,12,13,14', 'expect': '2025002'},
        {'openCode': '15,16,17,18,19,20,21', 'expect': '2025003'},
        # ... 更多数据
    ]
    
    # 运行第二阶段优化
    data_manager = MockDataManager()
    optimizer = PhaseTwoOptimizer(data_manager)
    results = optimizer.run_phase_two_optimization(mock_history_data)
    
    # 获取增强推荐
    recommendations = optimizer.get_enhanced_recommendations(mock_history_data, top_n=3)
    print(f"\n🎯 第二阶段增强推荐结果: {recommendations}")






