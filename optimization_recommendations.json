[{"period": "2025235", "recommended_zodiacs": ["蛇", "猪", "狗"], "actual_zodiacs": [], "timestamp": "2025-08-23 03:06:31", "method": "Complete Optimization Integration"}, {"period": "2025234", "recommended_zodiacs": ["蛇", "猪", "狗"], "actual_zodiacs": ["龙", "鼠", "猪", "羊", "羊", "虎", "蛇"], "timestamp": "2025-08-23 02:30:04", "method": "Backtest Optimization Integration"}, {"period": "2025233", "recommended_zodiacs": ["蛇", "猪", "狗"], "actual_zodiacs": ["猪", "牛", "兔", "鼠", "鸡", "鼠", "蛇"], "timestamp": "2025-08-23 02:30:04", "method": "Backtest Optimization Integration"}, {"period": "2025232", "recommended_zodiacs": ["蛇", "猪", "狗"], "actual_zodiacs": ["蛇", "虎", "猪", "狗", "鼠", "羊", "鼠"], "timestamp": "2025-08-23 02:30:04", "method": "Backtest Optimization Integration"}, {"period": "2025231", "recommended_zodiacs": ["蛇", "猪", "狗"], "actual_zodiacs": ["虎", "羊", "牛", "狗", "羊", "猪", "龙"], "timestamp": "2025-08-23 02:30:05", "method": "Backtest Optimization Integration"}, {"period": "2025230", "recommended_zodiacs": ["龙", "猪", "狗"], "actual_zodiacs": ["兔", "猴", "蛇", "猴", "马", "狗", "兔"], "timestamp": "2025-08-23 02:30:05", "method": "Backtest Optimization Integration"}, {"period": "2025229", "recommended_zodiacs": ["鸡", "猪", "狗"], "actual_zodiacs": ["牛", "马", "马", "鼠", "虎", "鼠", "龙"], "timestamp": "2025-08-23 02:30:05", "method": "Backtest Optimization Integration"}, {"period": "2025228", "recommended_zodiacs": ["蛇", "猪", "狗"], "actual_zodiacs": ["鸡", "狗", "虎", "猴", "龙", "虎", "兔"], "timestamp": "2025-08-23 02:30:06", "method": "Backtest Optimization Integration"}, {"period": "2025227", "recommended_zodiacs": ["蛇", "猪", "狗"], "actual_zodiacs": ["猪", "羊", "兔", "猪", "虎", "猪", "虎"], "timestamp": "2025-08-23 02:30:06", "method": "Backtest Optimization Integration"}, {"period": "2025226", "recommended_zodiacs": ["蛇", "猪", "狗"], "actual_zodiacs": ["蛇", "兔", "龙", "龙", "鼠", "猪", "猪"], "timestamp": "2025-08-23 02:30:06", "method": "Backtest Optimization Integration"}, {"period": "2025225", "recommended_zodiacs": ["蛇", "猪", "狗"], "actual_zodiacs": ["鸡", "马", "蛇", "狗", "鸡", "羊", "狗"], "timestamp": "2025-08-23 02:30:06", "method": "Backtest Optimization Integration"}, {"period": "2025224", "recommended_zodiacs": ["蛇", "猪", "狗"], "actual_zodiacs": ["猪", "鸡", "蛇", "猪", "蛇", "蛇", "虎"], "timestamp": "2025-08-23 02:30:07", "method": "Backtest Optimization Integration"}, {"period": "2025223", "recommended_zodiacs": ["猴", "猪", "狗"], "actual_zodiacs": ["猴", "牛", "狗", "猪", "猴", "马", "蛇"], "timestamp": "2025-08-23 02:30:07", "method": "Backtest Optimization Integration"}, {"period": "2025222", "recommended_zodiacs": ["猴", "猪", "狗"], "actual_zodiacs": ["猴", "虎", "鸡", "蛇", "蛇", "牛", "猪"], "timestamp": "2025-08-23 02:30:07", "method": "Backtest Optimization Integration"}, {"period": "2025221", "recommended_zodiacs": ["猴", "猪", "狗"], "actual_zodiacs": ["猴", "兔", "兔", "羊", "蛇", "马", "狗"], "timestamp": "2025-08-23 02:30:08", "method": "Backtest Optimization Integration"}, {"period": "2025220", "recommended_zodiacs": ["猴", "猪", "狗"], "actual_zodiacs": ["羊", "虎", "龙", "羊", "猴", "猪", "狗"], "timestamp": "2025-08-23 02:30:08", "method": "Backtest Optimization Integration"}, {"period": "2025219", "recommended_zodiacs": ["猴", "猪", "狗"], "actual_zodiacs": ["虎", "蛇", "羊", "猴", "虎", "牛", "鸡"], "timestamp": "2025-08-23 02:30:08", "method": "Backtest Optimization Integration"}, {"period": "2025218", "recommended_zodiacs": ["猴", "猪", "狗"], "actual_zodiacs": ["猴", "狗", "马", "猴", "龙", "龙", "羊"], "timestamp": "2025-08-23 02:30:09", "method": "Backtest Optimization Integration"}, {"period": "2025217", "recommended_zodiacs": ["猴", "猪", "狗"], "actual_zodiacs": ["鼠", "兔", "兔", "蛇", "狗", "羊", "猴"], "timestamp": "2025-08-23 02:30:09", "method": "Backtest Optimization Integration"}, {"period": "2025216", "recommended_zodiacs": ["马", "猪", "狗"], "actual_zodiacs": ["马", "猪", "狗", "马", "牛", "猴", "羊"], "timestamp": "2025-08-23 02:30:09", "method": "Backtest Optimization Integration"}, {"period": "2025215", "recommended_zodiacs": ["马", "猪", "狗"], "actual_zodiacs": ["鸡", "羊", "猪", "兔", "马", "蛇", "鸡"], "timestamp": "2025-08-23 02:30:09", "method": "Backtest Optimization Integration"}, {"period": "2025214", "recommended_zodiacs": ["马", "猪", "狗"], "actual_zodiacs": ["猴", "马", "龙", "虎", "蛇", "猪", "兔"], "timestamp": "2025-08-23 02:30:10", "method": "Backtest Optimization Integration"}, {"period": "2025213", "recommended_zodiacs": ["马", "猪", "狗"], "actual_zodiacs": ["兔", "猪", "猴", "龙", "羊", "马", "羊"], "timestamp": "2025-08-23 02:30:10", "method": "Backtest Optimization Integration"}, {"period": "2025212", "recommended_zodiacs": ["马", "猪", "狗"], "actual_zodiacs": ["牛", "马", "蛇", "羊", "马", "狗", "鼠"], "timestamp": "2025-08-23 02:30:10", "method": "Backtest Optimization Integration"}, {"period": "2025211", "recommended_zodiacs": ["马", "猪", "狗"], "actual_zodiacs": ["兔", "蛇", "龙", "兔", "马", "马", "龙"], "timestamp": "2025-08-23 02:30:11", "method": "Backtest Optimization Integration"}, {"period": "2025210", "recommended_zodiacs": ["马", "猪", "狗"], "actual_zodiacs": ["羊", "虎", "羊", "羊", "马", "龙", "兔"], "timestamp": "2025-08-23 02:30:11", "method": "Backtest Optimization Integration"}, {"period": "2025209", "recommended_zodiacs": ["马", "猪", "狗"], "actual_zodiacs": ["猪", "狗", "狗", "龙", "马", "龙", "蛇"], "timestamp": "2025-08-23 02:30:11", "method": "Backtest Optimization Integration"}, {"period": "2025208", "recommended_zodiacs": ["马", "猪", "狗"], "actual_zodiacs": ["猪", "牛", "马", "马", "狗", "牛", "鸡"], "timestamp": "2025-08-23 02:30:11", "method": "Backtest Optimization Integration"}, {"period": "2025207", "recommended_zodiacs": ["马", "猪", "狗"], "actual_zodiacs": ["鸡", "兔", "鼠", "蛇", "蛇", "鸡", "马"], "timestamp": "2025-08-23 02:30:12", "method": "Backtest Optimization Integration"}, {"period": "2025206", "recommended_zodiacs": ["马", "猪", "狗"], "actual_zodiacs": ["羊", "羊", "兔", "猪", "虎", "狗", "猴"], "timestamp": "2025-08-23 02:30:12", "method": "Backtest Optimization Integration"}, {"period": "2025205", "recommended_zodiacs": ["马", "猪", "狗"], "actual_zodiacs": ["羊", "虎", "马", "鼠", "蛇", "鼠", "蛇"], "timestamp": "2025-08-23 02:30:12", "method": "Backtest Optimization Integration"}]