"""
彩票预测系统 - 第一阶段优化实现
高优先级：集成学习框架、高级特征工程、自适应参数调整
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Any, Tuple, Optional
from collections import defaultdict, Counter
import math
import warnings
warnings.filterwarnings('ignore')

# 机器学习库
try:
    from sklearn.ensemble import RandomForestClassifier, GradientBoostingClassifier
    from sklearn.neural_network import MLPClassifier
    from sklearn.svm import SVC
    from sklearn.linear_model import LogisticRegression
    from sklearn.preprocessing import StandardScaler
    from sklearn.model_selection import cross_val_score
    from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score
    SKLEARN_AVAILABLE = True
except ImportError:
    SKLEARN_AVAILABLE = False
    print("警告: sklearn未安装，部分功能将不可用")

# XGBoost
try:
    import xgboost as xgb
    XGBOOST_AVAILABLE = True
except ImportError:
    XGBOOST_AVAILABLE = False
    print("警告: xgboost未安装，XGBoost功能将不可用")

# LightGBM
try:
    import lightgbm as lgb
    LIGHTGBM_AVAILABLE = True
except ImportError:
    LIGHTGBM_AVAILABLE = False
    print("警告: lightgbm未安装，LightGBM功能将不可用")

class AdvancedFeatureEngineering:
    """高级特征工程 - 第一阶段核心优化"""
    
    def __init__(self, data_manager):
        self.data_manager = data_manager
        self.feature_cache = {}
        self.scaler = StandardScaler()
        
    def create_advanced_features(self, history_data: List[Dict[str, Any]]) -> Dict[str, np.ndarray]:
        """创建高级特征"""
        print("🔧 开始高级特征工程...")
        
        features = {}
        
        # 1. 高阶统计特征
        features['higher_order_moments'] = self._calculate_higher_order_moments(history_data)
        
        # 2. 信息熵特征
        features['information_entropy'] = self._calculate_information_entropy(history_data)
        
        # 3. 互信息矩阵
        features['mutual_information_matrix'] = self._calculate_mutual_information_matrix(history_data)
        
        # 4. 时间序列特征
        features['time_series_features'] = self._extract_time_series_features(history_data)
        
        # 5. 组合特征
        features['combination_features'] = self._create_combination_features(history_data)
        
        # 6. 波动性特征
        features['volatility_features'] = self._calculate_volatility_features(history_data)
        
        print(f"✅ 特征工程完成，共生成 {sum(len(f) for f in features.values())} 个特征")
        return features
    
    def _calculate_higher_order_moments(self, history_data: List[Dict[str, Any]]) -> np.ndarray:
        """计算高阶统计矩"""
        zodiac_moments = {}
        zodiacs = {self.data_manager.get_zodiac_info(i).zodiac for i in range(1, self.data_manager.max_number + 1)}
        
        for zodiac in zodiacs:
            appearances = []
            for i, record in enumerate(history_data[:50]):
                nums = self._parse_lottery_numbers(record.get('openCode', ''))
                zodiac_appeared = any(self.data_manager.get_zodiac_info(n).zodiac == zodiac for n in nums)
                appearances.append(1 if zodiac_appeared else 0)
            
            if appearances:
                # 计算均值、方差、偏度、峰度
                mean_val = np.mean(appearances)
                var_val = np.var(appearances)
                skew_val = self._calculate_skewness(appearances)
                kurt_val = self._calculate_kurtosis(appearances)
                
                zodiac_moments[zodiac] = [mean_val, var_val, skew_val, kurt_val]
        
        # 转换为特征矩阵
        feature_matrix = []
        for zodiac in sorted(zodiacs):
            if zodiac in zodiac_moments:
                feature_matrix.extend(zodiac_moments[zodiac])
            else:
                feature_matrix.extend([0, 0, 0, 0])
        
        return np.array(feature_matrix)
    
    def _calculate_information_entropy(self, history_data: List[Dict[str, Any]]) -> np.ndarray:
        """计算信息熵特征"""
        zodiac_entropies = {}
        zodiacs = {self.data_manager.get_zodiac_info(i).zodiac for i in range(1, self.data_manager.max_number + 1)}
        
        for zodiac in zodiacs:
            # 计算该生肖的出现概率分布
            appearances = []
            for record in history_data[:30]:
                nums = self._parse_lottery_numbers(record.get('openCode', ''))
                zodiac_appeared = any(self.data_manager.get_zodiac_info(n).zodiac == zodiac for n in nums)
                appearances.append(zodiac_appeared)
            
            if appearances:
                # 计算信息熵
                prob = sum(appearances) / len(appearances)
                if prob > 0 and prob < 1:
                    entropy = -prob * math.log2(prob) - (1-prob) * math.log2(1-prob)
                else:
                    entropy = 0
                zodiac_entropies[zodiac] = entropy
        
        # 转换为特征向量
        feature_vector = [zodiac_entropies.get(z, 0) for z in sorted(zodiacs)]
        return np.array(feature_vector)
    
    def _calculate_mutual_information_matrix(self, history_data: List[Dict[str, Any]]) -> np.ndarray:
        """计算互信息矩阵"""
        zodiacs = sorted({self.data_manager.get_zodiac_info(i).zodiac for i in range(1, self.data_manager.max_number + 1)})
        n_zodiacs = len(zodiacs)
        mi_matrix = np.zeros((n_zodiacs, n_zodiacs))
        
        # 构建生肖出现矩阵
        appearance_matrix = []
        for record in history_data[:40]:
            row = []
            nums = self._parse_lottery_numbers(record.get('openCode', ''))
            for zodiac in zodiacs:
                appeared = any(self.data_manager.get_zodiac_info(n).zodiac == zodiac for n in nums)
                row.append(1 if appeared else 0)
            appearance_matrix.append(row)
        
        if appearance_matrix:
            appearance_matrix = np.array(appearance_matrix)
            
            # 计算互信息
            for i in range(n_zodiacs):
                for j in range(i+1, n_zodiacs):
                    mi = self._calculate_mutual_information(appearance_matrix[:, i], appearance_matrix[:, j])
                    mi_matrix[i, j] = mi
                    mi_matrix[j, i] = mi
        
        # 展平为特征向量
        return mi_matrix.flatten()
    
    def _extract_time_series_features(self, history_data: List[Dict[str, Any]]) -> np.ndarray:
        """提取时间序列特征"""
        zodiacs = {self.data_manager.get_zodiac_info(i).zodiac for i in range(1, self.data_manager.max_number + 1)}
        ts_features = []
        
        for zodiac in zodiacs:
            # 构建时间序列
            time_series = []
            for record in history_data[:60]:
                nums = self._parse_lottery_numbers(record.get('openCode', ''))
                appeared = any(self.data_manager.get_zodiac_info(n).zodiac == zodiac for n in nums)
                time_series.append(1 if appeared else 0)
            
            if time_series:
                # 计算时间序列特征
                features = [
                    np.mean(time_series),  # 均值
                    np.std(time_series),   # 标准差
                    np.max(time_series),   # 最大值
                    np.min(time_series),   # 最小值
                    self._calculate_autocorrelation(time_series, lag=1),  # 自相关
                    self._calculate_autocorrelation(time_series, lag=2),  # 自相关
                    self._count_consecutive_ones(time_series),  # 连续出现次数
                    self._count_consecutive_zeros(time_series), # 连续未出现次数
                ]
                ts_features.extend(features)
            else:
                ts_features.extend([0] * 8)
        
        return np.array(ts_features)
    
    def _create_combination_features(self, history_data: List[Dict[str, Any]]) -> np.ndarray:
        """创建组合特征"""
        # 生肖组合特征
        zodiac_pairs = []
        zodiacs = list({self.data_manager.get_zodiac_info(i).zodiac for i in range(1, self.data_manager.max_number + 1)})
        
        for i in range(len(zodiacs)):
            for j in range(i+1, len(zodiacs)):
                zodiac_pairs.append((zodiacs[i], zodiacs[j]))
        
        combination_features = []
        
        for pair in zodiac_pairs[:20]:  # 限制特征数量
            # 计算两个生肖同时出现的概率
            co_occurrence = 0
            total_periods = 0
            
            for record in history_data[:30]:
                nums = self._parse_lottery_numbers(record.get('openCode', ''))
                zodiacs_in_record = {self.data_manager.get_zodiac_info(n).zodiac for n in nums}
                
                if pair[0] in zodiacs_in_record and pair[1] in zodiacs_in_record:
                    co_occurrence += 1
                total_periods += 1
            
            co_prob = co_occurrence / total_periods if total_periods > 0 else 0
            combination_features.append(co_prob)
        
        return np.array(combination_features)
    
    def _calculate_volatility_features(self, history_data: List[Dict[str, Any]]) -> np.ndarray:
        """计算波动性特征"""
        zodiacs = {self.data_manager.get_zodiac_info(i).zodiac for i in range(1, self.data_manager.max_number + 1)}
        volatility_features = []
        
        for zodiac in zodiacs:
            # 计算不同时间窗口的波动性
            windows = [10, 20, 30]
            zodiac_volatility = []
            
            for window in windows:
                appearances = []
                for record in history_data[:window]:
                    nums = self._parse_lottery_numbers(record.get('openCode', ''))
                    appeared = any(self.data_manager.get_zodiac_info(n).zodiac == zodiac for n in nums)
                    appearances.append(1 if appeared else 0)
                
                if appearances:
                    # 计算波动性指标
                    volatility = np.std(appearances)
                    zodiac_volatility.append(volatility)
                else:
                    zodiac_volatility.append(0)
            
            volatility_features.extend(zodiac_volatility)
        
        return np.array(volatility_features)
    
    # 辅助方法
    def _parse_lottery_numbers(self, open_code: str) -> List[int]:
        """解析开奖号码"""
        if not open_code or open_code == 'N/A':
            return []
        try:
            return [int(num.strip()) for num in open_code.split(',') if num.strip().isdigit()]
        except (ValueError, AttributeError):
            return []
    
    def _calculate_skewness(self, data: List[float]) -> float:
        """计算偏度"""
        if len(data) < 3:
            return 0
        mean_val = np.mean(data)
        std_val = np.std(data)
        if std_val == 0:
            return 0
        skewness = np.mean([((x - mean_val) / std_val) ** 3 for x in data])
        return skewness
    
    def _calculate_kurtosis(self, data: List[float]) -> float:
        """计算峰度"""
        if len(data) < 4:
            return 0
        mean_val = np.mean(data)
        std_val = np.std(data)
        if std_val == 0:
            return 0
        kurtosis = np.mean([((x - mean_val) / std_val) ** 4 for x in data]) - 3
        return kurtosis
    
    def _calculate_mutual_information(self, x: np.ndarray, y: np.ndarray) -> float:
        """计算互信息"""
        if len(x) != len(y):
            return 0
        
        # 计算联合概率分布
        joint_counts = defaultdict(int)
        x_counts = defaultdict(int)
        y_counts = defaultdict(int)
        total = len(x)
        
        for i in range(total):
            joint_counts[(x[i], y[i])] += 1
            x_counts[x[i]] += 1
            y_counts[y[i]] += 1
        
        # 计算互信息
        mi = 0
        for (x_val, y_val), joint_count in joint_counts.items():
            if joint_count > 0:
                joint_prob = joint_count / total
                x_prob = x_counts[x_val] / total
                y_prob = y_counts[y_val] / total
                
                if x_prob > 0 and y_prob > 0:
                    mi += joint_prob * math.log2(joint_prob / (x_prob * y_prob))
        
        return mi
    
    def _calculate_autocorrelation(self, data: List[float], lag: int) -> float:
        """计算自相关"""
        if len(data) <= lag:
            return 0
        
        mean_val = np.mean(data)
        var_val = np.var(data)
        
        if var_val == 0:
            return 0
        
        autocorr = 0
        for i in range(len(data) - lag):
            autocorr += (data[i] - mean_val) * (data[i + lag] - mean_val)
        
        autocorr /= (len(data) - lag) * var_val
        return autocorr
    
    def _count_consecutive_ones(self, data: List[float]) -> int:
        """计算连续1的个数"""
        max_consecutive = 0
        current_consecutive = 0
        
        for val in data:
            if val == 1:
                current_consecutive += 1
                max_consecutive = max(max_consecutive, current_consecutive)
            else:
                current_consecutive = 0
        
        return max_consecutive
    
    def _count_consecutive_zeros(self, data: List[float]) -> int:
        """计算连续0的个数"""
        max_consecutive = 0
        current_consecutive = 0
        
        for val in data:
            if val == 0:
                current_consecutive += 1
                max_consecutive = max(max_consecutive, current_consecutive)
            else:
                current_consecutive = 0
        
        return max_consecutive


class AdvancedEnsembleFramework:
    """高级集成学习框架 - 第一阶段核心优化"""
    
    def __init__(self, data_manager):
        self.data_manager = data_manager
        self.models = {}
        self.meta_learner = None
        self.feature_engineering = AdvancedFeatureEngineering(data_manager)
        self.is_trained = False
        
        # 初始化基础模型
        self._initialize_models()
    
    def _initialize_models(self):
        """初始化基础模型"""
        if SKLEARN_AVAILABLE:
            self.models['random_forest'] = RandomForestClassifier(
                n_estimators=100, max_depth=10, random_state=42
            )
            self.models['gradient_boosting'] = GradientBoostingClassifier(
                n_estimators=100, max_depth=6, random_state=42
            )
            self.models['neural_network'] = MLPClassifier(
                hidden_layer_sizes=(100, 50), max_iter=500, random_state=42
            )
            self.models['svm'] = SVC(probability=True, random_state=42)
        
        if XGBOOST_AVAILABLE:
            self.models['xgboost'] = xgb.XGBClassifier(
                n_estimators=100, max_depth=6, random_state=42
            )
        
        if LIGHTGBM_AVAILABLE:
            self.models['lightgbm'] = lgb.LGBMClassifier(
                n_estimators=100, max_depth=6, random_state=42
            )
        
        # 元学习器
        if SKLEARN_AVAILABLE:
            self.meta_learner = LogisticRegression(random_state=42)
        
        print(f"✅ 初始化了 {len(self.models)} 个基础模型")
    
    def prepare_training_data(self, history_data: List[Dict[str, Any]]) -> Tuple[np.ndarray, np.ndarray]:
        """准备训练数据"""
        print("📊 准备训练数据...")
        
        # 生成高级特征
        features = self.feature_engineering.create_advanced_features(history_data)
        
        # 合并所有特征
        all_features = []
        for feature_name, feature_data in features.items():
            all_features.append(feature_data)
        
        X = np.concatenate(all_features)
        
        # 生成标签（下一期是否出现）
        y = []
        for i in range(len(history_data) - 1):
            current_record = history_data[i]
            next_record = history_data[i + 1]
            
            # 当前期的生肖
            current_nums = self.feature_engineering._parse_lottery_numbers(current_record.get('openCode', ''))
            current_zodiacs = {self.data_manager.get_zodiac_info(n).zodiac for n in current_nums}
            
            # 下一期的生肖
            next_nums = self.feature_engineering._parse_lottery_numbers(next_record.get('openCode', ''))
            next_zodiacs = {self.data_manager.get_zodiac_info(n).zodiac for n in next_nums}
            
            # 标签：当前期生肖在下一期是否出现
            label = 1 if current_zodiacs & next_zodiacs else 0
            y.append(label)
        
        # 确保数据长度一致
        min_len = min(len(X), len(y))
        X = X[:min_len]
        y = np.array(y[:min_len])
        
        print(f"✅ 训练数据准备完成: X.shape={X.shape}, y.shape={y.shape}")
        return X, y
    
    def train_ensemble(self, history_data: List[Dict[str, Any]]):
        """训练集成模型"""
        print("🤖 开始训练集成模型...")
        
        # 准备训练数据
        X, y = self.prepare_training_data(history_data)
        
        if len(X) == 0 or len(y) == 0:
            print("❌ 训练数据不足")
            return
        
        # 数据标准化
        X_scaled = self.feature_engineering.scaler.fit_transform(X.reshape(-1, 1)).flatten()
        
        # 第一层：训练基础模型
        base_predictions = {}
        for name, model in self.models.items():
            try:
                print(f"训练 {name}...")
                model.fit(X_scaled.reshape(-1, 1), y)
                
                # 交叉验证评估
                cv_scores = cross_val_score(model, X_scaled.reshape(-1, 1), y, cv=3)
                print(f"  {name} 交叉验证准确率: {cv_scores.mean():.3f} (+/- {cv_scores.std() * 2:.3f})")
                
                # 获取预测概率
                base_predictions[name] = model.predict_proba(X_scaled.reshape(-1, 1))[:, 1]
                
            except Exception as e:
                print(f"  {name} 训练失败: {e}")
                continue
        
        if not base_predictions:
            print("❌ 没有成功训练的基础模型")
            return
        
        # 第二层：训练元学习器
        if self.meta_learner and len(base_predictions) > 1:
            print("训练元学习器...")
            meta_features = np.column_stack(list(base_predictions.values()))
            self.meta_learner.fit(meta_features, y)
            
            # 评估元学习器
            meta_cv_scores = cross_val_score(self.meta_learner, meta_features, y, cv=3)
            print(f"元学习器交叉验证准确率: {meta_cv_scores.mean():.3f} (+/- {meta_cv_scores.std() * 2:.3f})")
        
        self.is_trained = True
        print("✅ 集成模型训练完成")
    
    def predict_ensemble(self, history_data: List[Dict[str, Any]]) -> Dict[str, float]:
        """集成预测"""
        if not self.is_trained:
            print("❌ 模型未训练")
            return {}
        
        # 生成特征
        features = self.feature_engineering.create_advanced_features(history_data)
        all_features = []
        for feature_data in features.values():
            all_features.append(feature_data)
        
        X = np.concatenate(all_features)
        X_scaled = self.feature_engineering.scaler.transform(X.reshape(-1, 1)).flatten()
        
        # 基础模型预测
        base_predictions = {}
        for name, model in self.models.items():
            try:
                base_predictions[name] = model.predict_proba(X_scaled.reshape(-1, 1))[:, 1]
            except:
                continue
        
        # 元学习器预测
        if self.meta_learner and len(base_predictions) > 1:
            meta_features = np.column_stack(list(base_predictions.values()))
            final_prediction = self.meta_learner.predict_proba(meta_features)[:, 1]
        else:
            # 如果没有元学习器，使用平均预测
            final_prediction = np.mean(list(base_predictions.values()), axis=0)
        
        # 转换为生肖预测
        zodiacs = {self.data_manager.get_zodiac_info(i).zodiac for i in range(1, self.data_manager.max_number + 1)}
        zodiac_predictions = {}
        
        for i, zodiac in enumerate(sorted(zodiacs)):
            if i < len(final_prediction):
                zodiac_predictions[zodiac] = float(final_prediction[i])
            else:
                zodiac_predictions[zodiac] = 0.0
        
        return zodiac_predictions


class AdaptiveParameterOptimizer:
    """自适应参数优化器 - 第一阶段核心优化"""
    
    def __init__(self):
        self.parameter_history = []
        self.performance_history = []
        self.current_params = self._get_default_params()
        self.best_params = self.current_params.copy()
        self.best_performance = 0.0
        self.optimization_count = 0
        
    def _get_default_params(self) -> Dict[str, Any]:
        """获取默认参数"""
        return {
            'blend_w': 0.71,
            'consec_cut': 5,
            'recent_count_cut': 6,
            'top_pool_k': 11,
            'alpha_ge2': 0.98,
            'diversity_bonus_two': 1.12,
            'diversity_bonus_three': 1.15,
            'hist_weight': 0.28,
            'hist_window': 48,
            'min_pair_lift_avg': 0.98,
        }
    
    def adapt_parameters(self, current_performance: float, history_data: List[Dict[str, Any]]) -> Dict[str, Any]:
        """自适应参数调整"""
        self.performance_history.append(current_performance)
        self.parameter_history.append(self.current_params.copy())
        
        # 性能下降检测
        if len(self.performance_history) >= 3:
            recent_performance = np.mean(self.performance_history[-3:])
            if recent_performance < self.best_performance * 0.95:  # 性能下降5%
                print(f"📉 检测到性能下降: {recent_performance:.3f} < {self.best_performance:.3f}")
                self._trigger_optimization(history_data)
        
        # 更新最佳性能
        if current_performance > self.best_performance:
            self.best_performance = current_performance
            self.best_params = self.current_params.copy()
            print(f"🎯 更新最佳性能: {self.best_performance:.3f}")
        
        return self.current_params
    
    def _trigger_optimization(self, history_data: List[Dict[str, Any]]):
        """触发参数优化"""
        self.optimization_count += 1
        print(f"🔄 触发第 {self.optimization_count} 次参数优化...")
        
        # 基于历史表现调整参数
        new_params = self._optimize_parameters(history_data)
        
        # 验证新参数
        if self._validate_parameters(new_params):
            self.current_params = new_params
            print("✅ 参数优化完成")
        else:
            print("❌ 参数验证失败，保持当前参数")
    
    def _optimize_parameters(self, history_data: List[Dict[str, Any]]) -> Dict[str, Any]:
        """优化参数"""
        # 基于历史性能趋势调整参数
        if len(self.performance_history) < 5:
            return self.current_params
        
        # 分析性能趋势
        recent_trend = np.polyfit(range(len(self.performance_history[-10:])), 
                                 self.performance_history[-10:], 1)[0]
        
        new_params = self.current_params.copy()
        
        if recent_trend < 0:  # 性能下降趋势
            # 增加保守性
            new_params['blend_w'] = min(0.9, new_params['blend_w'] + 0.05)
            new_params['consec_cut'] = max(3, new_params['consec_cut'] - 1)
            new_params['recent_count_cut'] = max(4, new_params['recent_count_cut'] - 1)
            new_params['alpha_ge2'] = min(1.0, new_params['alpha_ge2'] + 0.02)
        else:  # 性能上升趋势
            # 增加激进性
            new_params['blend_w'] = max(0.5, new_params['blend_w'] - 0.05)
            new_params['consec_cut'] = min(7, new_params['consec_cut'] + 1)
            new_params['recent_count_cut'] = min(8, new_params['recent_count_cut'] + 1)
            new_params['alpha_ge2'] = max(0.8, new_params['alpha_ge2'] - 0.02)
        
        return new_params
    
    def _validate_parameters(self, params: Dict[str, Any]) -> bool:
        """验证参数有效性"""
        # 检查参数范围
        if not (0.5 <= params['blend_w'] <= 0.9):
            return False
        if not (3 <= params['consec_cut'] <= 7):
            return False
        if not (4 <= params['recent_count_cut'] <= 8):
            return False
        if not (0.8 <= params['alpha_ge2'] <= 1.0):
            return False
        
        return True
    
    def get_optimization_report(self) -> Dict[str, Any]:
        """获取优化报告"""
        return {
            'optimization_count': self.optimization_count,
            'best_performance': self.best_performance,
            'current_performance': self.performance_history[-1] if self.performance_history else 0,
            'performance_trend': np.mean(self.performance_history[-5:]) if len(self.performance_history) >= 5 else 0,
            'best_params': self.best_params,
            'current_params': self.current_params
        }


class PhaseOneOptimizer:
    """第一阶段优化器主类"""
    
    def __init__(self, data_manager):
        self.data_manager = data_manager
        self.feature_engineering = AdvancedFeatureEngineering(data_manager)
        self.ensemble_framework = AdvancedEnsembleFramework(data_manager)
        self.parameter_optimizer = AdaptiveParameterOptimizer()
        
    def run_phase_one_optimization(self, history_data: List[Dict[str, Any]]) -> Dict[str, Any]:
        """运行第一阶段优化"""
        print("🚀 开始第一阶段优化...")
        print("=" * 60)
        
        results = {}
        
        # 1. 高级特征工程
        print("📊 步骤1: 高级特征工程")
        features = self.feature_engineering.create_advanced_features(history_data)
        results['features'] = features
        print(f"✅ 生成特征数量: {sum(len(f) for f in features.values())}")
        
        # 2. 集成学习框架
        print("\n🤖 步骤2: 集成学习框架")
        self.ensemble_framework.train_ensemble(history_data)
        ensemble_predictions = self.ensemble_framework.predict_ensemble(history_data)
        results['ensemble_predictions'] = ensemble_predictions
        
        # 3. 自适应参数优化
        print("\n⚙️ 步骤3: 自适应参数优化")
        # 模拟当前性能（实际应用中需要真实性能数据）
        current_performance = 0.75  # 假设当前性能为75%
        optimized_params = self.parameter_optimizer.adapt_parameters(current_performance, history_data)
        results['optimized_params'] = optimized_params
        
        # 4. 生成优化报告
        print("\n📈 步骤4: 生成优化报告")
        optimization_report = self.parameter_optimizer.get_optimization_report()
        results['optimization_report'] = optimization_report
        
        print("\n" + "=" * 60)
        print("✅ 第一阶段优化完成")
        print(f"🎯 最佳性能: {optimization_report['best_performance']:.3f}")
        print(f"🔄 优化次数: {optimization_report['optimization_count']}")
        
        return results
    
    def get_enhanced_recommendations(self, history_data: List[Dict[str, Any]], top_n: int = 3) -> List[Dict[str, Any]]:
        """获取增强推荐"""
        if not self.ensemble_framework.is_trained:
            print("❌ 集成模型未训练，请先运行优化")
            return []
        
        # 获取集成预测
        ensemble_predictions = self.ensemble_framework.predict_ensemble(history_data)
        
        # 排序并选择top_n
        sorted_predictions = sorted(ensemble_predictions.items(), key=lambda x: x[1], reverse=True)
        top_recommendations = sorted_predictions[:top_n]
        
        # 转换为推荐格式
        recommendations = []
        for zodiac, probability in top_recommendations:
            recommendations.append({
                'zodiac': zodiac,
                'probability': probability,
                'confidence': 'high' if probability > 0.7 else 'medium' if probability > 0.5 else 'low',
                'method': 'Phase One Enhanced Ensemble'
            })
        
        return recommendations


# 使用示例
if __name__ == "__main__":
    # 模拟数据管理器
    class MockDataManager:
        def get_zodiac_info(self, number):
            zodiacs = ['鼠', '牛', '虎', '兔', '龙', '蛇', '马', '羊', '猴', '鸡', '狗', '猪']
            return type('ZodiacInfo', (), {'zodiac': zodiacs[(number - 1) % 12]})()
    
    # 模拟历史数据
    mock_history_data = [
        {'openCode': '1,2,3,4,5,6,7', 'expect': '2025001'},
        {'openCode': '8,9,10,11,12,13,14', 'expect': '2025002'},
        {'openCode': '15,16,17,18,19,20,21', 'expect': '2025003'},
        # ... 更多数据
    ]
    
    # 运行第一阶段优化
    data_manager = MockDataManager()
    optimizer = PhaseOneOptimizer(data_manager)
    results = optimizer.run_phase_one_optimization(mock_history_data)
    
    # 获取增强推荐
    recommendations = optimizer.get_enhanced_recommendations(mock_history_data, top_n=3)
    print(f"\n🎯 增强推荐结果: {recommendations}")






